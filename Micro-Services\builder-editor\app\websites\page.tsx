'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Flex,
  Heading,
  Text,
  Button,
  Grid,
  Card,
  CardBody,
  CardFooter,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Avatar,
  HStack,
  VStack,
  Icon,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Input,
  Select,
  useToast,
  Spinner,
  Center
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  AddIcon,
  SettingsIcon,
  EditIcon,
  ExternalLinkIcon,
  CalendarIcon
} from '@chakra-ui/icons'
import { useRouter } from 'next/navigation'
import { loadUserWebsites, createWebsite, DatabaseWebsite } from '@/lib/supabase/sections'

// Brand colors matching the reference
const BRAND_COLORS = {
  primary: '#00d4aa',
  secondary: '#6c757d',
  background: '#f8f9fa',
  white: '#ffffff',
  text: '#343a40',
  border: '#dee2e6'
}

export default function WebsitesPage() {
  const [websites, setWebsites] = useState<DatabaseWebsite[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [newWebsiteName, setNewWebsiteName] = useState('')
  const [newWebsiteLanguage, setNewWebsiteLanguage] = useState<'en' | 'ar'>('en')
  const { isOpen, onOpen, onClose } = useDisclosure()
  const toast = useToast()
  const router = useRouter()

  // Mock user - in production this would come from auth
  const userId = 'user_123'
  const userName = 'John Doe'
  const userEmail = '<EMAIL>'

  useEffect(() => {
    loadWebsites()
  }, [])

  const loadWebsites = async () => {
    setLoading(true)
    try {
      const { websites: userWebsites, error } = await loadUserWebsites(userId)
      if (error) {
        toast({
          title: 'Error loading websites',
          description: error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } else {
        setWebsites(userWebsites)
      }
    } catch (error) {
      console.error('Error loading websites:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateWebsite = async () => {
    if (!newWebsiteName.trim()) {
      toast({
        title: 'Website name required',
        description: 'Please enter a name for your website',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    setCreating(true)
    try {
      const { website, error } = await createWebsite(userId, newWebsiteName.trim(), newWebsiteLanguage)
      if (error) {
        toast({
          title: 'Error creating website',
          description: error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } else if (website) {
        toast({
          title: 'Website created',
          description: `${website.name} has been created successfully`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        })
        setWebsites(prev => [website, ...prev])
        setNewWebsiteName('')
        setNewWebsiteLanguage('en')
        onClose()
      }
    } catch (error) {
      console.error('Error creating website:', error)
      toast({
        title: 'Error creating website',
        description: 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setCreating(false)
    }
  }

  const handleEditWebsite = (websiteId: string) => {
    router.push(`/editor?website=${websiteId}`)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Box minH="100vh" bg={BRAND_COLORS.background}>
      {/* Header Navigation */}
      <Box bg={BRAND_COLORS.white} borderBottom="1px" borderColor={BRAND_COLORS.border} shadow="sm">
        <Container maxW="7xl">
          <Flex h="60px" align="center" justify="space-between">
            {/* Logo and Navigation */}
            <HStack spacing={8}>
              <Flex align="center" gap={2}>
                <Box
                  w="32px"
                  h="32px"
                  bg={BRAND_COLORS.primary}
                  borderRadius="6px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  color="white"
                  fontWeight="bold"
                  fontSize="lg"
                >
                  W
                </Box>
                <Text fontSize="xl" fontWeight="bold" color={BRAND_COLORS.text}>
                  Builder
                </Text>
              </Flex>

              <HStack spacing={6}>
                <Text fontWeight="600" color={BRAND_COLORS.primary} borderBottom="2px" borderColor={BRAND_COLORS.primary} pb={1}>
                  My Websites
                </Text>
                <Text color={BRAND_COLORS.secondary} cursor="pointer" _hover={{ color: BRAND_COLORS.text }}>
                  My Stores
                </Text>
                <Text color={BRAND_COLORS.secondary} cursor="pointer" _hover={{ color: BRAND_COLORS.text }}>
                  Help Center
                </Text>
              </HStack>
            </HStack>

            {/* Right side - Language and User */}
            <HStack spacing={4}>
              {/* Language Switcher */}
              <Menu>
                <MenuButton as={Button} variant="ghost" rightIcon={<ChevronDownIcon />} size="sm">
                  English
                </MenuButton>
                <MenuList>
                  <MenuItem>English</MenuItem>
                  <MenuItem>العربية</MenuItem>
                </MenuList>
              </Menu>

              {/* User Menu */}
              <Menu>
                <MenuButton>
                  <Avatar size="sm" name={userName} bg={BRAND_COLORS.primary} />
                </MenuButton>
                <MenuList>
                  <MenuItem>
                    <VStack align="start" spacing={0}>
                      <Text fontWeight="600">{userName}</Text>
                      <Text fontSize="sm" color={BRAND_COLORS.secondary}>{userEmail}</Text>
                    </VStack>
                  </MenuItem>
                  <MenuItem icon={<SettingsIcon />}>Account Settings</MenuItem>
                  <MenuItem>Sign Out</MenuItem>
                </MenuList>
              </Menu>
            </HStack>
          </Flex>
        </Container>
      </Box>

      {/* Main Content */}
      <Container maxW="7xl" py={8}>
        {/* Page Header */}
        <Flex justify="space-between" align="center" mb={8}>
          <VStack align="start" spacing={1}>
            <Heading size="lg" color={BRAND_COLORS.text}>
              Manage Websites
            </Heading>
            <Text color={BRAND_COLORS.secondary}>
              Here's where you can manage, upgrade, or adjust settings for any of your domains.
            </Text>
          </VStack>

          <Button
            leftIcon={<AddIcon />}
            bg={BRAND_COLORS.primary}
            color="white"
            _hover={{ bg: '#00c29a' }}
            onClick={onOpen}
          >
            New Website
          </Button>
        </Flex>

        {/* Websites Grid */}
        {loading ? (
          <Center py={20}>
            <VStack spacing={4}>
              <Spinner size="xl" color={BRAND_COLORS.primary} />
              <Text color={BRAND_COLORS.secondary}>Loading your websites...</Text>
            </VStack>
          </Center>
        ) : websites.length === 0 ? (
          <Center py={20}>
            <VStack spacing={4}>
              <Text fontSize="lg" color={BRAND_COLORS.secondary}>
                No websites yet
              </Text>
              <Text color={BRAND_COLORS.secondary}>
                Create your first website to get started
              </Text>
              <Button
                leftIcon={<AddIcon />}
                bg={BRAND_COLORS.primary}
                color="white"
                _hover={{ bg: '#00c29a' }}
                onClick={onOpen}
              >
                Create Website
              </Button>
            </VStack>
          </Center>
        ) : (
          <Grid templateColumns="repeat(auto-fill, minmax(350px, 1fr))" gap={6}>
            {websites.map((website) => (
              <Card key={website.id} bg={BRAND_COLORS.white} shadow="md" _hover={{ shadow: 'lg' }}>
                <CardBody>
                  <VStack align="start" spacing={3}>
                    <HStack justify="space-between" w="full">
                      <HStack>
                        <Icon as={ExternalLinkIcon} color={BRAND_COLORS.primary} />
                        <Badge colorScheme="green" variant="subtle">
                          Website
                        </Badge>
                        {website.language === 'ar' && (
                          <Badge colorScheme="blue" variant="subtle">
                            Arabic
                          </Badge>
                        )}
                      </HStack>
                    </HStack>

                    <Heading size="md" color={BRAND_COLORS.text}>
                      {website.name}
                    </Heading>

                    <HStack spacing={4} fontSize="sm" color={BRAND_COLORS.secondary}>
                      <HStack>
                        <Icon as={CalendarIcon} />
                        <Text>Created {formatDate(website.created_at)}</Text>
                      </HStack>
                    </HStack>

                    {website.domain && (
                      <Text fontSize="sm" color={BRAND_COLORS.primary}>
                        {website.domain}
                      </Text>
                    )}
                  </VStack>
                </CardBody>

                <CardFooter pt={0}>
                  <HStack spacing={3} w="full">
                    <Button
                      leftIcon={<SettingsIcon />}
                      variant="outline"
                      size="sm"
                      flex={1}
                    >
                      Settings
                    </Button>
                    <Button
                      leftIcon={<EditIcon />}
                      bg={BRAND_COLORS.primary}
                      color="white"
                      _hover={{ bg: '#00c29a' }}
                      size="sm"
                      flex={1}
                      onClick={() => handleEditWebsite(website.id)}
                    >
                      Edit Site
                    </Button>
                  </HStack>
                </CardFooter>
              </Card>
            ))}
          </Grid>
        )}
      </Container>

      {/* Create Website Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Create New Website</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <Box w="full">
                <Text mb={2} fontWeight="600">
                  Website Name
                </Text>
                <Input
                  placeholder="Enter website name"
                  value={newWebsiteName}
                  onChange={(e) => setNewWebsiteName(e.target.value)}
                />
              </Box>

              <Box w="full">
                <Text mb={2} fontWeight="600">
                  Language
                </Text>
                <Select
                  value={newWebsiteLanguage}
                  onChange={(e) => setNewWebsiteLanguage(e.target.value as 'en' | 'ar')}
                >
                  <option value="en">English</option>
                  <option value="ar">العربية (Arabic)</option>
                </Select>
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              bg={BRAND_COLORS.primary}
              color="white"
              _hover={{ bg: '#00c29a' }}
              onClick={handleCreateWebsite}
              isLoading={creating}
              loadingText="Creating..."
            >
              Create Website
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}
